import re
import json
from urllib.parse import urljoin, urlparse
from bs4 import BeautifulSoup
from .client import client
from .constants import SRC_BASE_URL, SRC_AJAX_URL, USER_AGENT_HEADER
from .extractors import ExtractorFactory, ExtractorError
from typing import Dict, List, Optional, Union, Any

class AnimeEpisodeSourcesError(Exception):
    """Custom exception for anime episode sources scraping errors"""
    def __init__(self, message: str, function_name: str = "", status_code: int = 500):
        self.message = message
        self.function_name = function_name
        self.status_code = status_code
        super().__init__(self.message)

class Servers:
    """Server constants matching the TypeScript implementation"""
    VidStreaming = "vidstreaming"
    VidCloud = "vidcloud"
    StreamSB = "streamsb"
    StreamTape = "streamtape"

# Server ID mappings (from TypeScript implementation)
SERVER_ID_MAP = {
    Servers.VidStreaming: 4,  # vidtreaming -> 4
    Servers.VidCloud: 1,      # rapidcloud -> 1
    Servers.StreamSB: 5,      # streamsb -> 5
    Servers.StreamTape: 3,    # streamtape -> 3
}

def retrieve_server_id(soup: BeautifulSoup, server_index: int, category: str) -> Optional[str]:
    """
    Retrieve server ID from the HTML soup
    Equivalent to the TypeScript retrieveServerId function
    """
    try:
        selector = f".ps_-block.ps_-block-sub.servers-{category} > .ps__-list .server-item"
        server_items = soup.select(selector)
        
        for item in server_items:
            if item.get('data-server-id') == str(server_index):
                return item.get('data-id')
        
        return None
    except Exception:
        return None

def extract_episode_sources_from_url(
    server_url: str, 
    server: str = Servers.VidStreaming
) -> Dict[str, Any]:
    """
    Extract episode sources directly from a server URL using appropriate extractors
    """
    parsed_url = urlparse(server_url)
    
    try:
        # Use the extractor factory to get sources
        extracted_data = ExtractorFactory.extract_sources(server_url, server)
        
        # Set appropriate headers based on server type
        headers = {}
        if server == Servers.VidStreaming or server == Servers.VidCloud:
            headers = {"Referer": f"{parsed_url.scheme}://{parsed_url.netloc}/"}
        elif server == Servers.StreamSB:
            headers = {
                "Referer": server_url,
                "watchsb": "streamsb",
                "User-Agent": USER_AGENT_HEADER
            }
        elif server == Servers.StreamTape:
            headers = {
                "Referer": server_url,
                "User-Agent": USER_AGENT_HEADER
            }
        else:
            headers = {"Referer": server_url}
        
        # Merge extracted data with headers and additional info
        result = {
            "headers": headers,
            "sources": extracted_data.get("sources", []),
            "subtitles": extracted_data.get("subtitles", []),
            "intro": extracted_data.get("intro"),
            "outro": extracted_data.get("outro"),
            "download": None,
            "embedURL": server_url
        }
        
        return result
        
    except ExtractorError as e:
        # If extraction fails, return basic fallback
        return {
            "headers": {"Referer": f"{parsed_url.scheme}://{parsed_url.netloc}/"},
            "sources": [
                {
                    "url": server_url,
                    "type": "mp4",
                    "quality": "auto"
                }
            ],
            "subtitles": [],
            "intro": None,
            "download": None,
            "embedURL": server_url,
            "extractorError": str(e)
        }

def _get_anime_episode_sources(
    episode_id: str,
    server: str = Servers.VidStreaming,
    category: str = "sub"
) -> Dict[str, Any]:
    """
    Internal function to get anime episode sources
    Equivalent to the TypeScript _getAnimeEpisodeSources function
    """
    # If episode_id is already a URL, extract sources directly
    if episode_id.startswith("http"):
        return extract_episode_sources_from_url(episode_id, server)
    
    # Construct the episode URL
    ep_url = urljoin(SRC_BASE_URL, f"/watch/{episode_id}")
    
    try:
        # Extract episode ID from the URL
        if "?ep=" not in episode_id:
            raise AnimeEpisodeSourcesError(
                "Invalid episode ID format",
                "get_anime_episode_sources",
                400
            )
        
        ep_id = episode_id.split("?ep=")[1]
        
        # Get server information
        servers_url = f"{SRC_AJAX_URL}/v2/episode/servers?episodeId={ep_id}"
        
        response = client.get(servers_url, headers={
            "Referer": ep_url,
            "X-Requested-With": "XMLHttpRequest"
        })
        
        if response.status_code != 200:
            raise AnimeEpisodeSourcesError(
                f"Failed to fetch servers: {response.status_code}",
                "get_anime_episode_sources",
                response.status_code
            )
        
        data = response.json()
        soup = BeautifulSoup(data.get("html", ""), 'html.parser')
        
        # Get server ID based on the requested server
        server_index = SERVER_ID_MAP.get(server, 4)  # Default to VidStreaming
        server_id = retrieve_server_id(soup, server_index, category)
        
        if not server_id:
            # Try to find any available server
            available_servers = []
            for srv_name, srv_index in SERVER_ID_MAP.items():
                srv_id = retrieve_server_id(soup, srv_index, category)
                if srv_id:
                    available_servers.append(srv_name)
            
            error_msg = f"Couldn't find {server} server"
            if available_servers:
                error_msg += f". Available servers: {', '.join(available_servers)}"
            
            raise AnimeEpisodeSourcesError(
                error_msg,
                "get_anime_episode_sources",
                500
            )
        
        # Get the actual source link
        sources_url = f"{SRC_AJAX_URL}/v2/episode/sources?id={server_id}"
        
        sources_response = client.get(sources_url)
        
        if sources_response.status_code != 200:
            raise AnimeEpisodeSourcesError(
                f"Failed to fetch sources: {sources_response.status_code}",
                "get_anime_episode_sources",
                sources_response.status_code
            )
        
        sources_data = sources_response.json()
        link = sources_data.get("link")
        
        if not link:
            raise AnimeEpisodeSourcesError(
                "No source link found",
                "get_anime_episode_sources",
                404
            )
        
        # Recursively call with the actual link
        return _get_anime_episode_sources(link, server, category)
        
    except AnimeEpisodeSourcesError:
        raise
    except Exception as e:
        raise AnimeEpisodeSourcesError(
            f"Unexpected error: {str(e)}",
            "get_anime_episode_sources",
            500
        )

def get_anime_episode_sources(
    episode_id: str,
    server: str = Servers.VidStreaming,
    category: str = "sub"
) -> Dict[str, Any]:
    """
    Get anime episode sources with additional metadata
    Equivalent to the TypeScript getAnimeEpisodeSources function
    """
    try:
        # Validate inputs
        if not episode_id or "?ep=" not in episode_id:
            raise AnimeEpisodeSourcesError(
                "Invalid anime episode id",
                "get_anime_episode_sources",
                400
            )
        
        if not category.strip():
            raise AnimeEpisodeSourcesError(
                "Invalid anime episode category",
                "get_anime_episode_sources",
                400
            )
        
        # Extract anime URL for metadata
        anime_url = urljoin(SRC_BASE_URL, episode_id.split("?ep=")[0])
        
        # Get episode sources and anime metadata in parallel
        episode_src_data = _get_anime_episode_sources(episode_id, server, category)
        
        # Get anime metadata for anilist and mal IDs
        anilist_id = None
        mal_id = None
        
        try:
            anime_response = client.get(anime_url, headers={
                "Referer": SRC_BASE_URL,
                "User-Agent": USER_AGENT_HEADER,
                "X-Requested-With": "XMLHttpRequest"
            })
            
            if anime_response.status_code == 200:
                soup = BeautifulSoup(anime_response.text, 'html.parser')
                sync_data_element = soup.find(id="syncData")
                
                if sync_data_element and sync_data_element.get_text():
                    try:
                        sync_data = json.loads(sync_data_element.get_text())
                        anilist_id = sync_data.get("anilist_id")
                        mal_id = sync_data.get("mal_id")
                        
                        # Convert to int if they exist and are valid
                        if anilist_id:
                            anilist_id = int(anilist_id)
                        if mal_id:
                            mal_id = int(mal_id)
                    except (json.JSONDecodeError, ValueError):
                        pass
        except Exception:
            # If we can't get metadata, continue without it
            pass
        
        # Return the complete result
        result = {
            **episode_src_data,
            "anilistID": anilist_id,
            "malID": mal_id
        }
        
        return result
        
    except AnimeEpisodeSourcesError:
        raise
    except Exception as e:
        raise AnimeEpisodeSourcesError(
            f"Unexpected error: {str(e)}",
            "get_anime_episode_sources",
            500
        )

# Export the main function and server constants
__all__ = [
    'get_anime_episode_sources',
    'Servers',
    'AnimeEpisodeSourcesError'
]