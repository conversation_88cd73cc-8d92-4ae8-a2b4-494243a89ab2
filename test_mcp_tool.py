#!/usr/bin/env python3
"""
Test script for the MCP getAnimeEpisodeSources tool
"""

import asyncio
import sys
import os

# Add the current directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_mcp_tool():
    """Test the MCP tool directly"""
    print("🧪 Testing MCP getAnimeEpisodeSources Tool")
    print("=" * 60)
    
    try:
        # Import the MCP tool function
        from main import get_anime_episode_sources
        
        # Create a mock context (MCP tools expect a Context parameter)
        class MockContext:
            pass
        
        ctx = MockContext()
        
        # Test 1: Valid episode with vidstreaming
        print("\n📺 Test 1: Valid episode with vidstreaming server")
        print("-" * 50)
        
        result = await get_anime_episode_sources(
            ctx=ctx,
            episode_id="attack-on-titan-112?ep=1",
            server="vidstreaming",
            category="sub"
        )
        
        print(f"✅ Success: {result.get('success', False)}")
        if result.get('success'):
            data = result.get('data', {})
            print(f"   📊 Sources found: {len(data.get('sources', []))}")
            print(f"   🎬 Headers present: {bool(data.get('headers'))}")
            print(f"   📝 Subtitles: {len(data.get('subtitles', []))}")
            print(f"   🆔 AniList ID: {data.get('anilistID')}")
            print(f"   🆔 MAL ID: {data.get('malID')}")
            
            # Show first source if available
            sources = data.get('sources', [])
            if sources:
                first_source = sources[0]
                print(f"   🎥 First source quality: {first_source.get('quality')}")
                print(f"   🔗 First source URL: {first_source.get('url', '')[:50]}...")
        else:
            print(f"   ❌ Error: {result.get('error')}")
            print(f"   🔍 Error Type: {result.get('errorType')}")
        
        # Test 2: Valid episode with vidcloud
        print("\n📺 Test 2: Valid episode with vidcloud server")
        print("-" * 50)
        
        result2 = await get_anime_episode_sources(
            ctx=ctx,
            episode_id="attack-on-titan-112?ep=1",
            server="vidcloud",
            category="sub"
        )
        
        print(f"✅ Success: {result2.get('success', False)}")
        if result2.get('success'):
            data2 = result2.get('data', {})
            print(f"   📊 Sources found: {len(data2.get('sources', []))}")
            print(f"   🎬 Headers present: {bool(data2.get('headers'))}")
        else:
            print(f"   ❌ Error: {result2.get('error')}")
        
        # Test 3: Invalid server
        print("\n📺 Test 3: Invalid server")
        print("-" * 50)
        
        result3 = await get_anime_episode_sources(
            ctx=ctx,
            episode_id="attack-on-titan-112?ep=1",
            server="invalid_server",
            category="sub"
        )
        
        print(f"✅ Success: {result3.get('success', False)}")
        print(f"   ❌ Expected error: {result3.get('error')}")
        
        # Test 4: Empty episode ID
        print("\n📺 Test 4: Empty episode ID")
        print("-" * 50)
        
        result4 = await get_anime_episode_sources(
            ctx=ctx,
            episode_id="",
            server="vidstreaming",
            category="sub"
        )
        
        print(f"✅ Success: {result4.get('success', False)}")
        print(f"   ❌ Expected error: {result4.get('error')}")
        
        print("\n🎉 MCP Tool Testing Complete!")
        print("=" * 60)
        
        # Summary
        working_tests = sum([
            result.get('success', False),
            result2.get('success', False)
        ])
        
        print(f"📊 Summary: {working_tests}/2 main tests passed")
        print(f"🔧 Error handling: {'✅ Working' if not result3.get('success') and not result4.get('success') else '❌ Issues'}")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_mcp_tool())
