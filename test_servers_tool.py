#!/usr/bin/env python3
"""
Test script for the MCP get_available_servers tool
"""

import asyncio
import sys
import os

# Add the current directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_servers_tool():
    """Test the get_available_servers MCP tool"""
    print("🧪 Testing MCP get_available_servers Tool")
    print("=" * 60)
    
    try:
        # Import the MCP tool function
        from main import get_available_servers
        
        # Create a mock context
        class MockContext:
            pass
        
        ctx = MockContext()
        
        # Test the servers tool
        print("\n📡 Testing get_available_servers")
        print("-" * 40)
        
        result = await get_available_servers(ctx)
        
        print(f"✅ Success: {result.get('success', False)}")
        
        if result.get('success'):
            servers = result.get('servers', [])
            categories = result.get('categories', [])
            
            print(f"   📊 Available servers: {len(servers)}")
            print(f"   📂 Available categories: {len(categories)}")
            
            print("\n   🖥️  Servers:")
            for server in servers:
                print(f"      • {server.get('name')} ({server.get('id')})")
                print(f"        {server.get('description')}")
            
            print(f"\n   📂 Categories: {', '.join(categories)}")
        else:
            print(f"   ❌ Error: {result.get('error')}")
        
        print("\n🎉 Servers Tool Testing Complete!")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_servers_tool())
