#!/usr/bin/env python3
"""
Live test script for anime episode sources scraper
This script tests with real data from the hianime.sx website
"""

import sys
import json
import requests
from scrapers.animeEpisodeSources import get_anime_episode_sources, Servers, AnimeEpisodeSourcesError
from scrapers.constants import SRC_BASE_URL, SRC_AJAX_URL
from scrapers.client import client

def test_website_connectivity():
    """Test if the website is accessible"""
    print("Testing website connectivity...")
    print(f"Base URL: {SRC_BASE_URL}")
    print(f"Ajax URL: {SRC_AJAX_URL}")
    
    try:
        response = client.get(SRC_BASE_URL)
        print(f"✅ Website accessible (Status: {response.status_code})")
        return True
    except Exception as e:
        print(f"❌ Website not accessible: {str(e)}")
        return False

def find_sample_episode():
    """Try to find a sample episode ID from the homepage"""
    print("\nSearching for sample episodes...")
    
    try:
        # Try to get homepage content
        response = client.get(SRC_BASE_URL)
        if response.status_code != 200:
            print(f"❌ Failed to get homepage (Status: {response.status_code})")
            return None
        
        from bs4 import BeautifulSoup
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # Look for anime links in common selectors
        selectors = [
            '.film-detail .film-name .dynamic-name',
            '.flw-item .film-detail .film-name a',
            '.item .film-detail .film-name a',
            'a[href*="/watch/"]'
        ]
        
        for selector in selectors:
            links = soup.select(selector)
            for link in links:
                href = link.get('href', '')
                if href and '/watch/' in href:
                    # Extract anime ID and create episode ID
                    anime_id = href.replace('/watch/', '').split('?')[0]
                    episode_id = f"{anime_id}?ep=1"
                    print(f"✅ Found sample episode: {episode_id}")
                    return episode_id
        
        print("❌ No sample episodes found on homepage")
        return None
        
    except Exception as e:
        print(f"❌ Error finding sample episode: {str(e)}")
        return None

def test_episode_servers_endpoint(episode_id):
    """Test the episode servers endpoint directly"""
    print(f"\nTesting servers endpoint for: {episode_id}")
    
    try:
        # Extract episode number
        if "?ep=" not in episode_id:
            print("❌ Invalid episode ID format")
            return False
        
        ep_num = episode_id.split("?ep=")[1]
        servers_url = f"{SRC_AJAX_URL}/v2/episode/servers?episodeId={ep_num}"
        
        print(f"Servers URL: {servers_url}")
        
        response = client.get(servers_url, headers={
            "Referer": f"{SRC_BASE_URL}/watch/{episode_id}",
            "X-Requested-With": "XMLHttpRequest"
        })
        
        print(f"Response Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Servers endpoint working")
            print(f"Response keys: {list(data.keys())}")
            
            if 'html' in data:
                from bs4 import BeautifulSoup
                soup = BeautifulSoup(data['html'], 'html.parser')
                server_items = soup.select('.server-item')
                print(f"Found {len(server_items)} server items")
                
                for item in server_items[:3]:  # Show first 3
                    server_id = item.get('data-server-id', 'N/A')
                    data_id = item.get('data-id', 'N/A')
                    server_name = item.get_text(strip=True)
                    print(f"  Server: {server_name} (ID: {server_id}, Data: {data_id})")
            
            return True
        else:
            print(f"❌ Servers endpoint failed: {response.status_code}")
            try:
                error_data = response.json()
                print(f"Error response: {error_data}")
            except:
                print(f"Error response: {response.text[:200]}...")
            return False
            
    except Exception as e:
        print(f"❌ Error testing servers endpoint: {str(e)}")
        return False

def test_episode_sources_with_real_data():
    """Test episode sources with real data"""
    print("\n" + "="*60)
    print("TESTING EPISODE SOURCES WITH REAL DATA")
    print("="*60)
    
    # Test connectivity first
    if not test_website_connectivity():
        print("❌ Cannot proceed - website not accessible")
        return False
    
    # Find a sample episode
    sample_episode = find_sample_episode()
    if not sample_episode:
        # Use some common anime episode IDs as fallback
        fallback_episodes = [
            "attack-on-titan-112?ep=1",
            "demon-slayer-kimetsu-no-yaiba-47?ep=1",
            "one-piece-100?ep=1",
            "naruto-shippuden-355?ep=1"
        ]
        
        print("\nTrying fallback episode IDs...")
        for ep_id in fallback_episodes:
            if test_episode_servers_endpoint(ep_id):
                sample_episode = ep_id
                break
    
    if not sample_episode:
        print("❌ Could not find any working episode ID")
        return False
    
    # Test the servers endpoint
    if not test_episode_servers_endpoint(sample_episode):
        print("❌ Servers endpoint not working")
        return False
    
    # Test our scraper
    print(f"\n{'='*40}")
    print("TESTING OUR SCRAPER")
    print(f"{'='*40}")
    
    servers_to_test = [
        Servers.VidStreaming,
        Servers.VidCloud,
        Servers.StreamSB,
        Servers.StreamTape
    ]
    
    success_count = 0
    
    for server in servers_to_test:
        print(f"\nTesting {server} server...")
        print("-" * 30)
        
        try:
            result = get_anime_episode_sources(
                episode_id=sample_episode,
                server=server,
                category="sub"
            )
            
            print(f"✅ {server} server working!")
            print(f"   Sources: {len(result.get('sources', []))}")
            print(f"   Headers: {bool(result.get('headers'))}")
            print(f"   Subtitles: {len(result.get('subtitles', []))}")
            print(f"   AniList ID: {result.get('anilistID')}")
            print(f"   MAL ID: {result.get('malID')}")
            
            # Show first source
            sources = result.get('sources', [])
            if sources:
                first_source = sources[0]
                print(f"   First source type: {first_source.get('type', 'N/A')}")
                print(f"   First source quality: {first_source.get('quality', 'N/A')}")
                url = first_source.get('url', '')
                print(f"   First source URL: {url[:50]}{'...' if len(url) > 50 else ''}")
            
            success_count += 1
            
        except AnimeEpisodeSourcesError as e:
            print(f"⚠️  {server} server error: {e.message}")
            print(f"   Status Code: {e.status_code}")
            
        except Exception as e:
            print(f"❌ {server} unexpected error: {str(e)}")
    
    print(f"\n{'='*40}")
    print(f"RESULTS: {success_count}/{len(servers_to_test)} servers working")
    print(f"{'='*40}")
    
    return success_count > 0

def test_mcp_tool_format():
    """Test the MCP tool format"""
    print("\n" + "="*60)
    print("TESTING MCP TOOL FORMAT")
    print("="*60)
    
    # Import the MCP tool function
    try:
        import asyncio
        from main import get_anime_episode_sources as mcp_get_sources
        from mcp.server.fastmcp import Context
        
        # Create a mock context
        class MockContext:
            pass
        
        async def test_mcp():
            ctx = MockContext()
            
            # Test with invalid input
            result = await mcp_get_sources(ctx, "", "vidstreaming", "sub")
            print("Testing invalid input:")
            print(f"  Success: {result.get('success')}")
            print(f"  Error: {result.get('error', 'N/A')}")
            
            # Test with valid input (using a common episode)
            result = await mcp_get_sources(ctx, "attack-on-titan-112?ep=1", "vidstreaming", "sub")
            print("\nTesting valid input:")
            print(f"  Success: {result.get('success')}")
            if result.get('success'):
                data = result.get('data', {})
                print(f"  Sources: {len(data.get('sources', []))}")
                print(f"  Headers: {bool(data.get('headers'))}")
            else:
                print(f"  Error: {result.get('error', 'N/A')}")
                print(f"  Error Type: {result.get('errorType', 'N/A')}")
        
        # Run the async test
        asyncio.run(test_mcp())
        print("✅ MCP tool format test completed")
        
    except Exception as e:
        print(f"❌ MCP tool format test failed: {str(e)}")

if __name__ == "__main__":
    try:
        print("🚀 Starting Live Episode Sources Test")
        print("=" * 60)
        
        # Run the main test
        success = test_episode_sources_with_real_data()
        
        if success:
            # Test MCP format
            test_mcp_tool_format()
            
            print("\n🎉 All tests completed!")
            print("✅ Episode sources scraper is working with hianime.sx")
        else:
            print("\n❌ Tests failed - scraper needs debugging")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n\n⏹️  Testing interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n\n💥 Unexpected error during testing: {str(e)}")
        import traceback
        traceback.print_exc()
        sys.exit(1)