#!/usr/bin/env python3
"""
Test script for anime episode sources scraper
"""

import sys
import json
from scrapers.animeEpisodeSources import get_anime_episode_sources, Servers, AnimeEpisodeSourcesError

def test_episode_sources():
    """Test the episode sources scraper with a sample episode"""
    
    # Test data - you would need to replace with actual episode IDs
    test_cases = [
        {
            "episode_id": "attack-on-titan-112?ep=1",
            "server": Servers.VidStreaming,
            "category": "sub"
        },
        {
            "episode_id": "demon-slayer-kimetsu-no-yaiba-47?ep=1", 
            "server": Servers.VidCloud,
            "category": "sub"
        }
    ]
    
    print("Testing Anime Episode Sources Scraper")
    print("=" * 50)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\nTest Case {i}:")
        print(f"Episode ID: {test_case['episode_id']}")
        print(f"Server: {test_case['server']}")
        print(f"Category: {test_case['category']}")
        print("-" * 30)
        
        try:
            result = get_anime_episode_sources(
                episode_id=test_case['episode_id'],
                server=test_case['server'],
                category=test_case['category']
            )
            
            print("✅ Success!")
            print(f"Sources found: {len(result.get('sources', []))}")
            print(f"Headers: {bool(result.get('headers'))}")
            print(f"Subtitles: {len(result.get('subtitles', []))}")
            print(f"AniList ID: {result.get('anilistID')}")
            print(f"MAL ID: {result.get('malID')}")
            print(f"Embed URL: {result.get('embedURL', 'N/A')}")
            
            # Print first source if available
            sources = result.get('sources', [])
            if sources:
                print(f"First source URL: {sources[0].get('url', 'N/A')}")
                print(f"First source type: {sources[0].get('type', 'N/A')}")
            
        except AnimeEpisodeSourcesError as e:
            print(f"❌ AnimeEpisodeSourcesError: {e.message}")
            print(f"Function: {e.function_name}")
            print(f"Status Code: {e.status_code}")
            
        except Exception as e:
            print(f"❌ Unexpected Error: {str(e)}")
            print(f"Type: {type(e).__name__}")

def test_invalid_inputs():
    """Test error handling with invalid inputs"""
    
    print("\n\nTesting Error Handling")
    print("=" * 50)
    
    error_test_cases = [
        {
            "name": "Empty episode ID",
            "episode_id": "",
            "server": Servers.VidStreaming,
            "category": "sub"
        },
        {
            "name": "Invalid episode ID format",
            "episode_id": "invalid-episode-id",
            "server": Servers.VidStreaming,
            "category": "sub"
        },
        {
            "name": "Empty category",
            "episode_id": "test-anime?ep=1",
            "server": Servers.VidStreaming,
            "category": ""
        }
    ]
    
    for test_case in error_test_cases:
        print(f"\nTesting: {test_case['name']}")
        print("-" * 30)
        
        try:
            result = get_anime_episode_sources(
                episode_id=test_case['episode_id'],
                server=test_case['server'],
                category=test_case['category']
            )
            print("❌ Expected error but got success")
            
        except AnimeEpisodeSourcesError as e:
            print(f"✅ Expected error caught: {e.message}")
            
        except Exception as e:
            print(f"⚠️  Unexpected error type: {str(e)}")

def test_servers():
    """Test different server types"""
    
    print("\n\nTesting Different Servers")
    print("=" * 50)
    
    servers = [
        Servers.VidStreaming,
        Servers.VidCloud,
        Servers.StreamSB,
        Servers.StreamTape
    ]
    
    test_episode = "test-anime?ep=1"  # Replace with actual episode ID
    
    for server in servers:
        print(f"\nTesting server: {server}")
        print("-" * 30)
        
        try:
            result = get_anime_episode_sources(
                episode_id=test_episode,
                server=server,
                category="sub"
            )
            print(f"✅ {server} server working")
            
        except AnimeEpisodeSourcesError as e:
            print(f"⚠️  {server} server error: {e.message}")
            
        except Exception as e:
            print(f"❌ {server} unexpected error: {str(e)}")

if __name__ == "__main__":
    try:
        test_episode_sources()
        test_invalid_inputs()
        test_servers()
        
        print("\n\n" + "=" * 50)
        print("Testing completed!")
        print("Note: Some tests may fail if the episode IDs don't exist")
        print("Replace test episode IDs with actual ones for full testing")
        
    except KeyboardInterrupt:
        print("\n\nTesting interrupted by user")
        sys.exit(0)
    except Exception as e:
        print(f"\n\nUnexpected error during testing: {str(e)}")
        sys.exit(1)